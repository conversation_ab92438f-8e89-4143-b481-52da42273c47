from typing import List, Optional

from pydantic import BaseModel


class RainbowPlusRequest(BaseModel):
    prompts: list[str]
    target_llm: str
    num_samples: int = 5
    num_mutations: int = 3
    max_iters: int = 1
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    nickname: Optional[str] = None  # User nickname for Slack notifications
    project_id: Optional[str] = None  # Optional project association

class PointResponse(BaseModel):
    adv_prompt: str
    response: str
    score: float
    descriptor: str


class RainbowPlusResponse(BaseModel):
    points: List[PointResponse]


# Project-related Pydantic models
class ProjectCreateRequest(BaseModel):
    name: str
    description: str
    domain: str

class ProjectResponse(BaseModel):
    id: str
    name: str
    description: str
    domain: str
    created_at: str
    updated_at: str

class ProjectListResponse(BaseModel):
    projects: List[ProjectResponse]

# Enhanced project response for dashboard with computed statistics
class ProjectDashboardResponse(BaseModel):
    id: str
    name: str
    description: str
    domain: str
    status: str  # active, completed, pending
    datasetsGenerated: int  # Total number of prompts across all jobs
    testsRun: int  # Total number of completed jobs
    riskScore: float  # Average risk score from job results
    progress: int  # Progress percentage (0-100)
    created_at: str
    updated_at: str

class RecentProjectsResponse(BaseModel):
    projects: List[ProjectDashboardResponse]


class DatasetGenerationRequest(BaseModel):
    """Request model for dataset generation with dynamic configuration."""
    prompts: List[str]
    target_llm: str
    num_samples: Optional[int] = 10
    num_mutations: Optional[int] = 5
    max_iters: Optional[int] = 1
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    nickname: Optional[str] = None
    project_id: Optional[str] = None
    # Dynamic fields from client form
    application_description: Optional[str] = None
    example_input: Optional[str] = None


class DatasetGenerationResponse(BaseModel):
    """Response model for dataset generation."""
    job_id: str
    status: str
    message: str

class DatasetGenerationRequest(BaseModel):
    prompts: List[str]
    target_llm: str
    num_samples: int = 5
    num_mutations: int = 3
    max_iters: int = 1
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    nickname: Optional[str] = None
    project_id: Optional[str] = None
class ProjectDashboardListResponse(BaseModel):
    projects: List[ProjectDashboardResponse]


from sqlalchemy import Column, String, Integer, JSON, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class RainbowAnalysisJob(Base):
    __tablename__ = 'rainbow_analysis_jobs'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    status = Column(String(50), nullable=False, default='pending')
    prompts = Column(JSON, nullable=False)
    target_llm = Column(String(255), nullable=False)
    num_samples = Column(Integer, nullable=False)
    num_mutations = Column(Integer, nullable=False)
    max_iters = Column(Integer, nullable=False)
    api_key = Column(Text)
    base_url = Column(Text)
    nickname = Column(String(255))  # User nickname for Slack notifications
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id'), nullable=True)  # Optional project association

    # Progress tracking
    current_iteration = Column(Integer, default=0)
    current_sample = Column(Integer, default=0)
    total_iterations = Column(Integer)
    
    # Results
    results = Column(JSON)
    error_message = Column(Text)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # Relationships
    project = relationship("Project", back_populates="jobs")
    datasets = relationship("Dataset", back_populates="job")
    checkpoints = relationship("RainbowCheckpoint", back_populates="job")
    celery_tasks = relationship("RainbowCeleryTask", back_populates="job")

class Project(Base):
    __tablename__ = 'projects'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    domain = Column(String(100), nullable=False)

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    jobs = relationship("RainbowAnalysisJob", back_populates="project")
    datasets = relationship("Dataset", back_populates="project")

class RainbowCheckpoint(Base):
    __tablename__ = 'rainbow_checkpoints'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('rainbow_analysis_jobs.id'))
    iteration = Column(Integer, nullable=False)
    sample = Column(Integer, nullable=False)
    checkpoint_type = Column(String(50))
    data = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    job = relationship("RainbowAnalysisJob", back_populates="checkpoints")

class RainbowCeleryTask(Base):
    __tablename__ = 'rainbow_celery_tasks'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('rainbow_analysis_jobs.id'))
    celery_task_id = Column(String(255), nullable=False)
    task_type = Column(String(50))
    status = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow)
    
    job = relationship("RainbowAnalysisJob", back_populates="celery_tasks")

class Dataset(Base):
    __tablename__ = 'datasets'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('rainbow_analysis_jobs.id'), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id'), nullable=True)
    status = Column(String(50), nullable=True)
    content = Column(JSON, nullable=False)
    label = Column(String(255), nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    job = relationship("RainbowAnalysisJob", back_populates="datasets")
    project = relationship("Project", back_populates="datasets")